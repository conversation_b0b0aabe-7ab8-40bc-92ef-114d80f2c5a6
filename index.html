<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;300;400;500;600;700&family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">

</head>
<body>
    <header>
        <div class="header-top">
            <div class="header-top-wrapper">
                <div class="container">
                    <div class="header-info">
                        <div class="header-info-contact">
                            <p class="header-info__phone">call us:000000000 |&nbsp;&nbsp; </p>
                            <p class="header-info__email"> E-mail:<EMAIL></p>
                        </div>
                        <div class="header-info-social">
                            <p class="header-info__text">Feel to contact us</p>
                            <div class="header-info__icon">
                                <i class="fa-brands fa-facebook"></i>
                                <i class="fa-brands fa-square-twitter"></i>
                                <i class="fa-brands fa-linkedin"></i>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

           <div class="container">
            <nav class="nav">
                <div class="nav-logo">
                    <img src="images/logo.png" class="nav-logo__image" alt="">
                </div>

                <div class="nav-content">
                    <ul class="menu">
                        <li class="menu__item active"><a href="#" class="menu__link"><span class="menu__text">home</span></a></li>
                        <li class="menu__item"><a href="#" class="menu__link"><span class="menu__text">about</span></a></li>
                        <li class="menu__item"><a href="#" class="menu__link"><span class="menu__text">process</span></a></li>
                        <li class="menu__item"><a href="#" class="menu__link"><span class="menu__text">journal</span></a></li>
                        <li class="menu__item"><a href="#" class="menu__link"><span class="menu__text">contact</span></a></li>
                    </ul>
                    <div class="nav-search">
                        <i class="fa-solid fa-magnifying-glass"></i>
                    </div>
                </div>
            </nav>
           </div>

        </div>

    </header>

    <!-- qode-home-slider-5 Section -->
    <section class="qode-home-slider-5" id="qode-home-slider-5">
        <div class="slider-container">
            <!-- Slide 1 -->
            <div class="slide active" data-bg="images/slide01.jpg">
                <div class="slide-overlay"></div>
                <div class="slide-content">
                    <div class="container">
                        <h1 class="slide-title">AMAZING PRESENTATION</h1>
                        <p class="slide-description">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam posuere vulputate tincidunt. Maecenas tellus nisl, consequat vel dignissim id, suscipit eu neqquam vehicula.</p>
                        <div class="slide-buttons">
                            <a href="#" class="btn btn-primary">VIEW MORE</a>
                            <a href="#" class="btn btn-secondary">VIEW MORE</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2 -->
            <div class="slide" data-bg="images/slide02.jpg">
                <div class="slide-overlay"></div>
                <div class="slide-content">
                    <div class="container">
                        <h1 class="slide-title">WELCOME TO BRIDGE</h1>
                        <p class="slide-description">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam posuere vulputate tincidunt. Maecenas tellus nisl, consequat vel dignissim id, suscipit eu neqquam vehicula.</p>
                        <div class="slide-buttons">
                            <a href="#" class="btn btn-primary">VIEW MORE</a>
                            <a href="#" class="btn btn-secondary">VIEW MORE</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Dots -->
        <div class="slider-navigation">
            <div class="container">
                <div class="nav-dots">
                    <span class="nav-dot active" data-slide="0">1 / 2</span>
                    <span class="nav-dot" data-slide="1">2 / 2</span>
                </div>
            </div>
        </div>
    </section>

    <main>main</main>
    <footer>footer</footer>

    <script>
        // Slider functionality
        document.addEventListener('DOMContentLoaded', function() {
            const slides = document.querySelectorAll('.slide');
            const navDots = document.querySelectorAll('.nav-dot');
            let currentSlide = 0;
            const slideInterval = 5000; // 5 seconds

            // Set background images
            slides.forEach(slide => {
                const bgImage = slide.getAttribute('data-bg');
                slide.style.backgroundImage = `url('${bgImage}')`;
            });

            // Function to show specific slide
            function showSlide(index) {
                // Remove active class from all slides and dots
                slides.forEach(slide => slide.classList.remove('active'));
                navDots.forEach(dot => dot.classList.remove('active'));

                // Add active class to current slide and dot
                slides[index].classList.add('active');
                navDots[index].classList.add('active');

                currentSlide = index;
            }

            // Function to go to next slide
            function nextSlide() {
                const next = (currentSlide + 1) % slides.length;
                showSlide(next);
            }

            // Add click event listeners to navigation dots
            navDots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    showSlide(index);
                });
            });

            // Auto-advance slides
            setInterval(nextSlide, slideInterval);

            // Initialize first slide
            showSlide(0);
        });
    </script>
</body>
</html>