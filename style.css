/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
/*start-project/*

/* Document
   ========================================================================== */

/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */

 html {
    line-height: 1.15; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
  }

  /* Sections
     ========================================================================== */

  /**
   * Remove the margin in all browsers.
   */

  body {
    margin: 0;
  }

  /**
   * Render the `main` element consistently in IE.
   */

  main {
    display: block;
  }

  /**
   * Correct the font size and margin on `h1` elements within `section` and
   * `article` contexts in Chrome, Firefox, and Safari.
   */

  h1 {
    font-size: 2em;
    margin: 0.67em 0;
  }

  /* Grouping content
     ========================================================================== */

  /**
   * 1. Add the correct box sizing in Firefox.
   * 2. Show the overflow in Edge and IE.
   */

  hr {
    box-sizing: content-box; /* 1 */
    height: 0; /* 1 */
    overflow: visible; /* 2 */
  }

  /**
   * 1. Correct the inheritance and scaling of font size in all browsers.
   * 2. Correct the odd `em` font sizing in all browsers.
   */

  pre {
    font-family: monospace, monospace; /* 1 */
    font-size: 1em; /* 2 */
  }

  /* Text-level semantics
     ========================================================================== */

  /**
   * Remove the gray background on active links in IE 10.
   */

  a {
    background-color: transparent;
  }

  /**
   * 1. Remove the bottom border in Chrome 57-
   * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
   */

  abbr[title] {
    border-bottom: none; /* 1 */
    text-decoration: underline; /* 2 */
    text-decoration: underline dotted; /* 2 */
  }

  /**
   * Add the correct font weight in Chrome, Edge, and Safari.
   */

  b,
  strong {
    font-weight: bolder;
  }

  /**
   * 1. Correct the inheritance and scaling of font size in all browsers.
   * 2. Correct the odd `em` font sizing in all browsers.
   */

  code,
  kbd,
  samp {
    font-family: monospace, monospace; /* 1 */
    font-size: 1em; /* 2 */
  }

  /**
   * Add the correct font size in all browsers.
   */

  small {
    font-size: 80%;
  }

  /**
   * Prevent `sub` and `sup` elements from affecting the line height in
   * all browsers.
   */

  sub,
  sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }

  sub {
    bottom: -0.25em;
  }

  sup {
    top: -0.5em;
  }

  /* Embedded content
     ========================================================================== */

  /**
   * Remove the border on images inside links in IE 10.
   */

  img {
    border-style: none;
  }

  /* Forms
     ========================================================================== */

  /**
   * 1. Change the font styles in all browsers.
   * 2. Remove the margin in Firefox and Safari.
   */

  button,
  input,
  optgroup,
  select,
  textarea {
    font-family: inherit; /* 1 */
    font-size: 100%; /* 1 */
    line-height: 1.15; /* 1 */
    margin: 0; /* 2 */
  }

  /**
   * Show the overflow in IE.
   * 1. Show the overflow in Edge.
   */

  button,
  input { /* 1 */
    overflow: visible;
  }

  /**
   * Remove the inheritance of text transform in Edge, Firefox, and IE.
   * 1. Remove the inheritance of text transform in Firefox.
   */

  button,
  select { /* 1 */
    text-transform: none;
  }

  /**
   * Correct the inability to style clickable types in iOS and Safari.
   */

  button,
  [type="button"],
  [type="reset"],
  [type="submit"] {
    -webkit-appearance: button;
  }

  /**
   * Remove the inner border and padding in Firefox.
   */

  button::-moz-focus-inner,
  [type="button"]::-moz-focus-inner,
  [type="reset"]::-moz-focus-inner,
  [type="submit"]::-moz-focus-inner {
    border-style: none;
    padding: 0;
  }

  /**
   * Restore the focus styles unset by the previous rule.
   */

  button:-moz-focusring,
  [type="button"]:-moz-focusring,
  [type="reset"]:-moz-focusring,
  [type="submit"]:-moz-focusring {
    outline: 1px dotted ButtonText;
  }

  /**
   * Correct the padding in Firefox.
   */

  fieldset {
    padding: 0.35em 0.75em 0.625em;
  }

  /**
   * 1. Correct the text wrapping in Edge and IE.
   * 2. Correct the color inheritance from `fieldset` elements in IE.
   * 3. Remove the padding so developers are not caught out when they zero out
   *    `fieldset` elements in all browsers.
   */

  legend {
    box-sizing: border-box; /* 1 */
    color: inherit; /* 2 */
    display: table; /* 1 */
    max-width: 100%; /* 1 */
    padding: 0; /* 3 */
    white-space: normal; /* 1 */
  }

  /**
   * Add the correct vertical alignment in Chrome, Firefox, and Opera.
   */

  progress {
    vertical-align: baseline;
  }

  /**
   * Remove the default vertical scrollbar in IE 10+.
   */

  textarea {
    overflow: auto;
  }

  /**
   * 1. Add the correct box sizing in IE 10.
   * 2. Remove the padding in IE 10.
   */

  [type="checkbox"],
  [type="radio"] {
    box-sizing: border-box; /* 1 */
    padding: 0; /* 2 */
  }

  /**
   * Correct the cursor style of increment and decrement buttons in Chrome.
   */

  [type="number"]::-webkit-inner-spin-button,
  [type="number"]::-webkit-outer-spin-button {
    height: auto;
  }

  /**
   * 1. Correct the odd appearance in Chrome and Safari.
   * 2. Correct the outline style in Safari.
   */

  [type="search"] {
    -webkit-appearance: textfield; /* 1 */
    outline-offset: -2px; /* 2 */
  }

  /**
   * Remove the inner padding in Chrome and Safari on macOS.
   */

  [type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  /**
   * 1. Correct the inability to style clickable types in iOS and Safari.
   * 2. Change font properties to `inherit` in Safari.
   */

  ::-webkit-file-upload-button {
    -webkit-appearance: button; /* 1 */
    font: inherit; /* 2 */
  }

  /* Interactive
     ========================================================================== */

  /*
   * Add the correct display in Edge, IE 10+, and Firefox.
   */

  details {
    display: block;
  }

  /*
   * Add the correct display in all browsers.
   */

  summary {
    display: list-item;
  }

  /* Misc
     ========================================================================== */

  /**
   * Add the correct display in IE 10+.
   */

  template {
    display: none;
  }

  /**
   * Add the correct display in IE 10.
   */

  [hidden] {
    display: none;
  }

  /*------------------- start-project -----------------*/
  html{
    box-sizing: border-box;
  }
  *,*::before,*::after{
    box-sizing: inherit;
  }
h1,h2,h3,h4,h5,p,ul,li{
  margin: 0;
  padding: 0;
}
body{
  font-family:'Open Sans', sans-serif;;
}
a{
  text-decoration: none;
}
li{
  list-style: none;
}

/*--------------------- header-top ---------------------*/
.container{
  max-width: 70rem;
  margin: 0 auto;
}
.header-top-wrapper{
  background-color: #333745;
}
.header-info{
  /* background-color: #333745; */
  color: #fff;
  padding: 11px 0px;
  display: flex;
  justify-content: space-between;
  font-size: 0.7rem;
  font-weight: 300;
}
.header-info-contact{
  display: flex;
}
.header-info__email{
  color:#f2d03b
}
.header-info-social{
  display: flex;
}
.header-info__text,.header-info__icon{
  padding: 0 5px;
}

/******************** nav ******************/
.nav{
  display: flex;
  justify-content: space-between;
}
.menu{
  display: flex;
}
.nav-content{
  display: flex;
  align-items: center;
}
.nav-logo{
  max-width: 8rem;
}
.nav-logo__image{
  width: 100%;
}
.menu__link{
  display: inline-block;
  padding: 30px 20px;
  color: #898989;
  font-size: 0.9rem ;
  text-transform: uppercase;
}
.menu__link:hover .menu__text{
  color: #f2d03b;
  border-bottom: 2px solid #f2d03b;
}
.menu__text{
  border-bottom: 2px solid transparent;
  transition: all 400ms;
}
.nav-search i{
  color: #898989;
}
.nav-search:hover i{
  color: #333745;
  cursor: pointer;
}
.active .menu__text{
  color: #f2d03b;
}

/*--------------------- qode-home-slider-5 ---------------------*/
.qode-home-slider-5 {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.slider-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide.active {
  opacity: 1;
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.slide-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 20px;
}

.slide-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  line-height: 1.2;
}

.slide-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  font-weight: 300;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.slide-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  display: inline-block;
  padding: 12px 30px;
  text-decoration: none;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 1px;
  border: 2px solid;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-primary {
  background-color: #f2d03b;
  color: #333;
  border-color: #f2d03b;
}

.btn-primary:hover {
  background-color: transparent;
  color: #f2d03b;
}

.btn-secondary {
  background-color: transparent;
  color: white;
  border-color: white;
}

.btn-secondary:hover {
  background-color: white;
  color: #333;
}

.slider-navigation {
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
  z-index: 3;
}

.nav-dots {
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.nav-dot {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
  cursor: pointer;
  transition: color 0.3s ease;
  font-weight: 300;
}

.nav-dot.active {
  color: #f2d03b;
}

.nav-dot:hover {
  color: white;
}

/* Responsive Design for Slider */
@media (max-width: 768px) {
  .slide-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .slide-description {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .slide-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
  }

  .btn {
    padding: 10px 25px;
    font-size: 0.8rem;
  }

  .nav-dots {
    gap: 1rem;
  }

  .slider-navigation {
    bottom: 20px;
  }
}

@media (max-width: 480px) {
  .slide-title {
    font-size: 2rem;
    letter-spacing: 1px;
  }

  .slide-description {
    font-size: 0.9rem;
    padding: 0 10px;
  }

  .slide-content {
    padding: 0 15px;
  }
}